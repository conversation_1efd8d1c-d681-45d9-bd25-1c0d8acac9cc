<div class="relative {{ box.css_class }}" style="background-color: {{ box.background_color }};">
    {% if box.background_img %}
        <div class="absolute inset-0 overflow-hidden">
            <img src="{{ box.background_img | media | resize(1920, auto, { 'extension': 'webp' }) }}" alt="{{ box.background_img_title }}" class="h-full w-full object-cover">
        </div>
    {% endif %}

    <div class="relative container" data-boxes-container data-rel="boxes-wrapper">
        <div class="space-y-6 lg:space-y-0 lg:justify-start lg:space-y-0 lg:grid lg:grid-cols-2 lg:gap-12 pt-32 lg:pt-44">
            <div class="items-center space-y-6 lg:space-y-12 xl:w-5/6">
                <div class="text-center lg:text-start">
                    <h1 class="text-gray-800">{{ box.title }} <span class="text-primary-800">{{ box.branche }}</span>
                    </h1>
                </div>
                <div class="content_section_hero text-center lg:text-start text-gray-500">
                    {{ box.content | content }}
                </div>
                {% if box.benefits %}
                <div class="flex flex-col space-y-2 md:w-3/4 md:mx-auto lg:w-full">
                    {% for benefit in box.benefits %}
                    <div class="flex items-center">
                        <i class="fa-solid fa-check text-primary-700 pr-4 text-xl"></i>
                        <div class="content_section_hero text-gray-500">{{ benefit.title | content }}</div>
                    </div>
                    {% endfor %}
                </div>
                {% endif %}
            </div>
            <div class="bg-gradient-to-b from-primary to-primary/50 rounded-2xl h-fit">
                <div class="">{{ box.embed | raw }}</div>
            </div>
        </div>
    </div>
</div>
