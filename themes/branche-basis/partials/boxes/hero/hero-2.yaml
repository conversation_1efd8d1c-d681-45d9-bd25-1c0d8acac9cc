handle: hero-2
name: Hero-2
section: Hero's
icon: /plugins/offline/boxes/assets/img/boxes/image.svg

spacing:
    - general

form:
    tabs:
        icons:
            Algemeen: icon-header
            Instellingen: icon-cog
            Buttons: icon-external-link-square
        fields:
            title:
                label: Titel
                tab: Algemeen
            branche:
                label: Branche
                tab: Algemeen
            content:
                label: Content
                type: richeditor
                tab: Algemeen
            benefits:
                label: Voordeel
                prompt: Voeg een Voordeel toe
                type: repeater
                itemsExpanded: false
                tab: Algemeen
                titleFrom: title
                form:
                    fields:
                        title:
                            label: Titel
                            tab: Algemeen
                            type: richeditor
            embed:
                type: codeeditor
                size: huge
            background_color:
                type: mixin
                tab: Design
            background_img:
                label: Achtergrond Afbeelding
                type: mediafinder
                tab: Design
            img_title:
                label: Afbeelding titel (Alt-tag)
                tab: Design
            css_class:
                label: CSS class
                tab: Design
