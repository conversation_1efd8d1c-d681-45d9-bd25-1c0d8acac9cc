<?php

namespace Invato\Testimonials\Models;

use Model;
use October\Rain\Database\Traits\SoftDelete;
use October\Rain\Database\Traits\Sortable;
use October\Rain\Database\Traits\Validation;
use RainLab\Translate\Behaviors\TranslatableModel;

class Review extends Model
{
    // Begin Skeleton model
    use SoftDelete;
    use Sortable;
    use Validation;

    public $table = 'invato_testimonials_reviews';

    // https://laravel.com/docs/10.x/eloquent-mutators#attribute-casting
    protected $casts = [
        'id' => 'integer',
        'name' => 'string',
        'date' => 'date',
        'score' => 'integer',
        'review' => 'string',
        'is_approved' => 'boolean',
        'sort_order' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    protected $fillable = [
        'name',
        'date',
        'score',
        'review',
        'is_approved',
        'sort_order',
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    // https://laravel.com/docs/10.x/validation#available-validation-rules
    public $rules = [
        'name' => ['required', 'string', 'max:255'],
        'date' => ['nullable', 'date'],
        'score' => ['nullable', 'integer', 'min:1', 'max:5'],
        'review' => ['nullable', 'string'],
        'is_approved' => ['boolean'],
        'sort_order' => ['integer'],
    ];

    // translatable
    public $implement = [
        TranslatableModel::class,
    ];

    public $translatable = [
        'name',
        'review',
    ];

    // These attributes should not be in $casts and $rules
    protected $jsonable = [];

    // END Skeleton model

    // BEGIN Model specific
    // END Model specific
}
