<?php

namespace Invato\Blog\Models;

use Cms\Classes\Controller;
use Cms\Classes\Page as CmsPage;
use Cms\Classes\Theme;
use Invato\Blog\Components\CategoryDetail;
use Invato\Redirects\traits\CanRedirectModelTrait;
use Invato\Seo\traits\HasSeoableTrait;
use Invato\Traits\HasPageFinderTrait;
use Model;
use October\Rain\Database\Traits\SimpleTree;
use October\Rain\Database\Traits\Sluggable;
use October\Rain\Database\Traits\SoftDelete;
use October\Rain\Database\Traits\Sortable;
use October\Rain\Database\Traits\Validation;
use RainLab\Translate\Behaviors\TranslatableModel;

class Category extends Model
{
    // BEGIN Skeleton model
    use CanRedirectModelTrait;
    use HasPageFinderTrait;
    use HasSeoableTrait;
    use SimpleTree;
    use Sluggable;
    use SoftDelete;
    use Sortable;
    use Validation;

    protected static function booted(): void
    {
        static::deleting(static function (self $category) {
            static::createRedirect(
                plugin: 'blog',
                modelRecord: $category,
                detailPageController: CategoryDetail::class,
            );
        });

        static::restored(static function (self $category) {
            static::deleteRedirect($category);
        });
    }

    public $table = 'invato_blog_category';

    // https://laravel.com/docs/10.x/eloquent-mutators#attribute-casting
    protected $casts = [
        'id' => 'integer',
        'parent_id' => 'integer',
        'title' => 'string',
        'slug' => 'string',
        'description' => 'string',
        'sort_order' => 'integer',
        'img_title' => 'string',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    protected $fillable = [
        'parent_id',
        'title',
        'slug',
        'description',
        'sort_order',
        'img_title',
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    // https://laravel.com/docs/10.x/validation#available-validation-rules
    public $rules = [
        'title' => ['required', 'string', 'max:255'],
        'slug' => ['required', 'string', 'max:255', 'unique:invato_blog_category,slug,{{id}}'],
        'description' => ['nullable', 'string'],
        'sort_order' => ['integer'],
        'img_title' => ['nullable', 'string', 'max:255'],
    ];

    // translatable
    public $implement = [
        TranslatableModel::class,
    ];

    public $translatable = [
        'title',
        'slug',
        'description',
        'img_title',
    ];

    protected array $slugs = [
        'slug' => 'title',
    ];

    public $belongsToMany = [
        'posts' => [Post::class, 'table' => 'invato_blog_cat_post'],
    ];

    public $mediaAttributes = [
        'image',
    ];

    /**
     * Get PageFinder configuration for Category model
     */
    protected static function getPageFinderConfig(): array
    {
        return [
            'single_type' => 'blog-category',
            'all_type' => 'all-blog-categories',
            'component' => 'CategoryDetail',
        ];
    }

    // END Skeleton model

    // BEGIN Model specific
    public function getParentOptions()
    {
        $items = $this->id ? Category::withoutSelf()->get() : Category::all();

        return [
            // null key for "no parent"
            null => '('.trans('invato.blog::lang.category.noparent').')',
        ] + $items->lists('title', 'id');
    }
    // END Model specific

    public static function getMenuTypeInfo($type)
    {
        $result = [];

        if ($type == 'blog-category') {
            $references = [];

            $posts = self::orderBy('title')->get();
            foreach ($posts as $post) {
                $references[$post->id] = $post->title;
            }

            $result = [
                'references' => $references,
                'nesting' => false,
                'dynamicItems' => false,
            ];
        }

        if ($type == 'all-blog-categories') {
            $result = [
                'nesting' => true,
                'dynamicItems' => true,
            ];
        }

        if ($result) {
            $theme = Theme::getActiveTheme();

            $pages = CmsPage::listInTheme($theme, true);
            $cmsPages = [];

            foreach ($pages as $page) {
                if (! $page->hasComponent('categoryDetail')) {
                    continue;
                }

                $properties = $page->getComponentProperties('categoryDetail');
                if (! preg_match('/{{\s*:/', $properties['slug'])) {
                    continue;
                }

                $cmsPages[] = $page;
            }

            $result['cmsPages'] = $cmsPages;
        }

        return $result;
    }

    public static function resolveMenuItem($item, $url, $theme)
    {
        $result = null;

        if ($item->type == 'blog-category') {
            $model = Category::find($item->reference);

            if (! $model) {
                return;
            }

            $controller = new Controller($theme);
            $pageUrl = $controller->pageUrl($item->cmsPage, [
                'id' => $model->id,
                'slug' => $model->slug,
            ]);

            $result = [
                'url' => $pageUrl,
                'isActive' => $pageUrl == $url,
                'title' => $model->title,
                'mtime' => $model->updated_at,
            ];

            return $result;
        } elseif ($item->type == 'all-blog-categories') {
            $result = [
                'items' => [],
            ];

            $posts = self::orderBy('title')->get();
            $controller = new Controller($theme);

            foreach ($posts as $post) {
                $pageUrl = $controller->pageUrl($item->cmsPage, [
                    'id' => $post->id,
                    'slug' => $post->slug,
                ]);

                $postItem = [
                    'title' => $post->title,
                    'url' => $pageUrl,
                    'mtime' => $post->updated_at,
                ];

                $postItem['isActive'] = $postItem['url'] == $url;

                $result['items'][] = $postItem;
            }
        }

        return $result;
    }
}
