<?php

namespace Invato\Portfolio\Models;

use Invato\Portfolio\Components\CategoryDetail;
use Invato\Redirects\traits\CanRedirectModelTrait;
use Invato\Seo\traits\HasSeoableTrait;
use Invato\Traits\HasPageFinderTrait;
use Model;
use October\Rain\Database\Traits\SimpleTree;
use October\Rain\Database\Traits\Sluggable;
use October\Rain\Database\Traits\SoftDelete;
use October\Rain\Database\Traits\Sortable;
use October\Rain\Database\Traits\Validation;
use RainLab\Translate\Behaviors\TranslatableModel;

/**
 * Model
 */
class Category extends Model
{
    // BEGIN Skeleton model
    use CanRedirectModelTrait;
    use HasPageFinderTrait;
    use HasSeoableTrait;
    use SimpleTree;
    use Sluggable;
    use SoftDelete;
    use Sortable;
    use Validation;

    protected static function booted(): void
    {
        static::deleting(static function (self $category) {
            static::createRedirect(
                plugin: 'portfolio',
                modelRecord: $category,
                detailPageController: CategoryDetail::class,
            );
        });

        static::restored(static function (self $category) {
            static::deleteRedirect($category);
        });
    }

    public $implement = [
        TranslatableModel::class,
    ];

    public $translatable = ['title', 'description'];

    public $table = 'invato_portfolio_categories';

    // https://laravel.com/docs/10.x/eloquent-mutators#attribute-casting
    protected $casts = [
        'id' => 'integer',
        'title' => 'string',
        'slug' => 'string',
        'description' => 'string',
        'overview_image' => 'string',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    protected $fillable = [
        'title',
        'slug',
        'description',
        'overview_image',
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    // https://laravel.com/docs/10.x/validation#available-validation-rules
    public $rules = [
        'title' => ['required', 'string', 'max:255'],
        'slug' => ['required', 'string', 'max:255', 'unique:invato_portfolio_categories,slug,{{id}}'],
        'description' => ['nullable', 'string'],
        'overview_image' => ['nullable', 'string', 'max:255'],
    ];

    protected array $slugs = [
        'slug' => 'title',
    ];

    public $belongsToMany = [
        'projects' => [
            Project::class,
            'table' => 'invato_portfolio_cat_proj',
        ],
    ];

    // END Skeleton model

    // BEGIN Model specific
    protected static function getPageFinderConfig(): array
    {
        return [
            'single_type' => 'portfolio-category',
            'all_type' => 'all-portfolio-categories',
            'component' => 'PortfolioCategoryDetail',
        ];
    }
    // END Model specific
}
