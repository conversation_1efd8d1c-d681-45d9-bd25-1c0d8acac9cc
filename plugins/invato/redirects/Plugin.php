<?php

namespace Invato\Redirects;

use Cms\Classes\CmsController;
use Invato\Redirects\Classes\RedirectMiddleware;
use Invato\Redirects\Models\Redirect;
use System\Classes\PluginBase;

/**
 * Plugin class
 */
class Plugin extends PluginBase
{
    /**
     * register method, called when the plugin is first registered.
     */
    public function register(): void {}

    /**
     * boot method, called right before the request route.
     */
    public function boot(): void
    {
        // Redirect middleware
        CmsController::extend(static function ($controller) {
            $controller->middleware(RedirectMiddleware::class);
        });
    }

    /**
     * registerComponents used by the frontend.
     */
    public function registerComponents(): array
    {
        return [];
    }

    /**
     * registerSettings used by the backend.
     */
    public function registerSettings(): array
    {
        return [];
    }

    /**
     * Get PageFinder configuration for this plugin
     */
    protected function getPageFinderConfig(): array
    {
        return [
            'model' => Redirect::class,
            'menu_types' => [
                'redirect-detail' => 'invato.redirects::lang.menuitem.redirect_detail',
                'all-redirects' => 'invato.redirects::lang.menuitem.all_redirects',
            ],
        ];
    }
}
